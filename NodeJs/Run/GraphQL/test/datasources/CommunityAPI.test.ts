import nock from 'nock'
import { Environment } from '../../src/common/environment'
import { CommunityAPI } from '../../src/datasources/CommunityAPI'
import { CommunityResponse } from '../../src/generated/api'
import { CommunityModel } from '../../src/models/community'
import { Currency, UserProfileType } from '../../src/generated/resolvers-types'
import { userResponse } from './test-utils'

beforeEach(() => {
    nock.disableNetConnect()
})

describe('data source: CommunityAPI', () => {
    describe('method: getCommunity', () => {
        test('should make a call to get a community and correctly map it', async () => {
            // given
            const communityId = 'community-id'
            const underTest = new CommunityAPI(Environment.DEVEL)
            const scope = nock('https://devel-api-90568653510.europe-west1.run.app')
                .get(`/v1/communities/${communityId}`)
                .reply(200, communityResponse(communityId))

            // when
            const community = await underTest.getCommunity(communityId)

            // then
            expect(community).toEqual<CommunityModel>({
                id: communityId,
                name: 'Test Community',
                description: 'A test community description',
                slug: 'test-community',
                ownerId: 'owner-id',
                owner: {
                    id: 'owner-id',
                    name: 'user-name',
                    bio: 'user-bio',
                    image: {
                        url: 'image-id',
                        width: 420,
                        height: 69,
                        hidden: true,
                    },
                    path: 'user-path',
                    subscribable: true,
                    verified: false,
                    counts: {
                        supporting: 150,
                        supporters: 21231,
                        supportersThreshold: 25000,
                        posts: 243,
                    },
                    hasRssFeed: true,
                    spotifyShowUri: 'spotify-uri',
                    tier: {
                        id: 'EUR05',
                        priceCents: 500,
                        hidden: false,
                        default: false,
                        currency: Currency.EUR,
                    },
                    categories: [
                        {
                            id: 'category-id',
                            name: 'category-name',
                            slug: 'slug',
                        },
                    ],
                    isDeleted: false,
                    privacyPolicyEnabled: false,
                    analytics: {
                        facebookPixelId: 'facebook-pixel-id',
                    },
                    hasGiftsAllowed: true,
                    emailPublic: '<EMAIL>',
                    profileType: UserProfileType.PUBLIC,
                },
                membersCount: 42,
                image: {
                    url: 'community-image-id',
                    width: 800,
                    height: 600,
                    hidden: false,
                },
                createdAt: '2023-07-17T00:00:00Z',
                isVerified: true,
            })
            expect(scope.isDone()).toBeTruthy()
        })
    })
})

function communityResponse(communityId: string): CommunityResponse {
    return {
        id: communityId,
        name: 'Test Community',
        description: 'A test community description',
        slug: 'test-community',
        ownerId: 'owner-id',
        owner: userResponse('owner-id'),
        membersCount: 42,
        image: {
            id: 'community-image-id',
            width: 800,
            height: 600,
            hidden: false,
        },
        createdAt: '2023-07-17T00:00:00Z',
        isVerified: true,
    }
}
